import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppCommonModule } from '@app/shared/common/app-common.module';
import { UtilsModule } from '@shared/utils/utils.module';
import { CountoModule } from 'angular2-counto';
import { ModalModule, TabsModule, TooltipModule, BsDropdownModule, PopoverModule } from 'ngx-bootstrap';
import { AutoCompleteModule, InputMaskModule } from 'primeng/primeng';
import { NgxMaskModule } from 'ngx-mask';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FileUploadModule } from 'ng2-file-upload';



// Transaction Management Routing
import { TransactionManagementRoutingModule } from './transaction-management-routing.module';

// Transaction Components
import { TransactionsComponent } from '../transactions/transactions.component';
import { CreateOrEditTransactionModalComponent } from '../transactions/create-or-edit-transaction-modal.component';
import { ViewTransactionModalComponent } from '../transactions/view-transaction-modal.component';
import { TransactionComponent } from '../transaction/transaction.component';
import { TransactionDetailComponent } from '../transaction-detail/transaction-detail.component';
import { TransactionCoinBaselineComponent } from '../transaction-coin/transaction-coin.component';
import { TransactionPointBaselineComponent } from '../transaction-point/transaction-point.component';

// Member Transaction Components
import { MemberPointTransactionComponent } from '../member-point-transaction/member-point-transaction.component';

// Action Transaction Components
import { ActionTransactionComponent } from '../action-transaction/action-transaction.component';
import { OrigirinalActionTransactionComponent } from '../origirinal-action-transaction/origirinal-action-transaction.component';

// Referral Transaction Components
import { ReferralTransactionComponent } from '../referral-transaction/referral-transaction.component';

// Error Transaction Components
import { ErrorTransactionComponent } from '../error-transaction/error-transaction.component';

// Point Adjustment Components
import { SinglePointAdjustmentComponent } from '../point-adjustment/single/single-point-adjustment.component';
import { BatchPointAdjustmentComponent } from '../point-adjustment/batch/batch-point-adjustment.component';

// Redemption Management Components
import { RedemptionManagementComponent } from '../redemption-management/redemption-management.component';

// Action Import Components
import { ActionImportComponent } from '../action-importing/action-import/action-import.component';

// Input References Components
import { InputReferencesComponent } from '../input-references/input-references.component';
import { ViewInputReferencesComponent } from '../input-references/view-input-references/view-input-references.component';

// Import Member Mapping Components
import { ImportMemberMappingComponent } from '../import-member-mapping/import-member-mapping.component';
import { ImportMemberMappingModalComponent } from '../import-member-mapping/import-member-mapping-modal.component';

// Redeem Source Components
import { RedeemSourceManagementComponent } from '../redeemSource/redeemSource.component';
import { RedeemSourceNonePointComponent } from '../redeem-source-none-point/redeem-source-none-point.component';

// Service Account Components
import { ViewTransactionComponent } from '../service-account/view-transaction.component';
import { CreateOrEditServiceAccountModalComponent } from '../service-account/create-or-edit-service-account-modal.component';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        AppCommonModule,
        UtilsModule,
        CountoModule,
        ModalModule.forRoot(),
        TabsModule.forRoot(),
        TooltipModule.forRoot(),
        BsDropdownModule.forRoot(),
        PopoverModule.forRoot(),
        AutoCompleteModule,
        InputMaskModule,
        NgxMaskModule.forRoot(),
        MatCheckboxModule,
        MatTooltipModule,
        MatDialogModule,
        MatExpansionModule,
        NgxMatSelectSearchModule,
        FileUploadModule,

        // Transaction Management Routing
        TransactionManagementRoutingModule
    ],
    declarations: [
        // Transaction Components
        TransactionsComponent,
        CreateOrEditTransactionModalComponent,
        ViewTransactionModalComponent,
        TransactionComponent,
        TransactionDetailComponent,
        TransactionCoinBaselineComponent,
        TransactionPointBaselineComponent,
        
        // Member Transaction Components
        MemberPointTransactionComponent,
        
        // Action Transaction Components
        ActionTransactionComponent,
        OrigirinalActionTransactionComponent,
        
        // Referral Transaction Components
        ReferralTransactionComponent,
        
        // Error Transaction Components
        ErrorTransactionComponent,
        
        // Point Adjustment Components
        SinglePointAdjustmentComponent,
        BatchPointAdjustmentComponent,
        
        // Redemption Management Components
        RedemptionManagementComponent,
        
        // Action Import Components
        ActionImportComponent,
        
        // Input References Components
        InputReferencesComponent,
        ViewInputReferencesComponent,
        
        // Import Member Mapping Components
        ImportMemberMappingComponent,
        ImportMemberMappingModalComponent,
        
        // Redeem Source Components
        RedeemSourceManagementComponent,
        RedeemSourceNonePointComponent,

        // Service Account Components
        ViewTransactionComponent,
        CreateOrEditServiceAccountModalComponent
    ]
})
export class TransactionManagementModule { }
